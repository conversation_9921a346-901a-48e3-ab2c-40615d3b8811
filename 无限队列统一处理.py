#!/usr/bin/env python3
"""
无限队列统一处理工具 - 抠图+裁剪一体化
流程：RMBG-1.4抠图 → Haar裁剪 → SAM备用裁剪
结果保存在原图片同目录下，可以无限制添加文件到队列，静默处理
"""

import os
import sys
import glob
import threading
import queue
import time
import cv2
import numpy as np
from PIL import Image
import shutil
from datetime import datetime
from pathlib import Path

# 添加抠图工具路径
sys.path.append('../auto_matting_tool')

def clean_path(path):
    """清理路径中的特殊字符"""
    path = path.strip()
    if path.startswith('"') and path.endswith('"'):
        path = path[1:-1]  # 去掉首尾引号
    
    # 处理转义字符
    path = path.replace('\\ ', ' ')  # 处理转义的空格
    path = path.replace('\\(', '(')  # 处理转义的左括号
    path = path.replace('\\)', ')')  # 处理转义的右括号
    return path

class UnifiedProcessor:
    def __init__(self):
        self.task_queue = queue.Queue()
        self.processing = False
        self.current_task = None
        self.total_processed = 0
        self.total_failed = 0
        self.haar_success = 0
        self.sam_success = 0
        self.worker_thread = None
        self.running = True
        self.paused = False
        
        # 创建临时目录
        self.temp_dir = "temp_processing"
        os.makedirs(self.temp_dir, exist_ok=True)
        
    def start_worker(self):
        """启动工作线程"""
        self.worker_thread = threading.Thread(target=self.worker)
        self.worker_thread.daemon = True
        self.worker_thread.start()
    
    def worker(self):
        """工作线程 - 持续处理队列中的任务"""
        while self.running:
            try:
                # 检查是否暂停
                if self.paused:
                    time.sleep(1)
                    continue

                # 从队列获取任务，超时1秒
                task = self.task_queue.get(timeout=1)
                self.current_task = task
                self.processing = True

                # 处理任务
                if task['type'] == 'single':
                    success, method = self.process_single_image_silent(task['path'])
                    # 单文件处理的统计
                    if success:
                        self.total_processed += 1
                        if "haar" in method.lower():
                            self.haar_success += 1
                        elif "sam" in method.lower():
                            self.sam_success += 1
                    else:
                        self.total_failed += 1
                        print(f"❌ 处理失败: {os.path.basename(task['path'])} - {method}")

                elif task['type'] == 'batch':
                    success, method = self.process_batch_silent(task['path'])
                    # 批量处理的统计在 process_batch_silent 中已经处理
                    if not success:
                        print(f"❌ 批量处理失败: {os.path.basename(task['path'])} - {method}")
                else:
                    success, method = False, "未知任务类型"
                    self.total_failed += 1
                    print(f"❌ 处理失败: {os.path.basename(task['path'])} - {method}")

                self.current_task = None
                self.processing = False
                self.task_queue.task_done()

            except queue.Empty:
                continue
            except Exception as e:
                print(f"❌ 处理异常: {e}")
                self.processing = False
                self.current_task = None
                self.task_queue.task_done()  # 重要：标记任务完成
    
    def add_task(self, path, task_type):
        """添加任务到队列"""
        task = {'path': path, 'type': task_type}
        self.task_queue.put(task)
        self.show_queue_status()
    
    def show_queue_status(self):
        """显示队列状态"""
        queue_size = self.task_queue.qsize()

        if self.processing and not self.paused:
            current_name = os.path.basename(self.current_task['path']) if self.current_task else "未知"
            print(f"🔄 正在处理: {current_name} | 队列: {queue_size} | 完成: {self.total_processed} | Haar: {self.haar_success} | SAM: {self.sam_success} | 失败: {self.total_failed}")
        elif self.paused:
            print(f"⏸️ 队列已暂停 | 队列: {queue_size} | 完成: {self.total_processed} | Haar: {self.haar_success} | SAM: {self.sam_success} | 失败: {self.total_failed}")
        else:
            if queue_size > 0:
                print(f"⏳ 队列: {queue_size} | 完成: {self.total_processed} | Haar: {self.haar_success} | SAM: {self.sam_success} | 失败: {self.total_failed}")
            else:
                print(f"✅ 队列空闲 | 完成: {self.total_processed} | Haar: {self.haar_success} | SAM: {self.sam_success} | 失败: {self.total_failed}")

    def clear_queue(self):
        """清空队列"""
        while not self.task_queue.empty():
            try:
                self.task_queue.get_nowait()
                self.task_queue.task_done()
            except queue.Empty:
                break
        return True

    def pause(self):
        """暂停处理"""
        self.paused = True
        return True

    def resume(self):
        """恢复处理"""
        self.paused = False
        return True
    
    def rmbg_matting(self, image_path, edge_padding=15):
        """使用RMBG-1.4进行抠图，抠图时就保留边缘"""
        try:
            from rembg import remove, new_session
            import cv2
            from io import BytesIO

            # 创建会话
            session = new_session('rmbg-1.4')

            # 读取原图
            with open(image_path, 'rb') as f:
                input_data = f.read()

            # 先用RMBG得到精确的mask
            output_data = remove(input_data, session=session)
            matted_image = Image.open(BytesIO(output_data)).convert('RGBA')

            # 获取RMBG的alpha通道（精确轮廓）
            alpha_array = np.array(matted_image.split()[-1])

            # 对轮廓进行膨胀操作，扩展边缘
            if edge_padding > 0:
                kernel = np.ones((edge_padding * 2 + 1, edge_padding * 2 + 1), np.uint8)
                expanded_alpha = cv2.dilate(alpha_array, kernel, iterations=1)
            else:
                expanded_alpha = alpha_array

            # 用扩展后的轮廓直接抠原图
            original_image = Image.open(image_path).convert('RGBA')
            original_array = np.array(original_image)

            # 应用扩展后的alpha通道
            result_array = original_array.copy()
            result_array[:, :, 3] = expanded_alpha

            result_image = Image.fromarray(result_array, 'RGBA')

            # 保存临时文件
            temp_filename = f"temp_{int(time.time())}_{os.path.basename(image_path)}.png"
            temp_path = os.path.join(self.temp_dir, temp_filename)

            result_image.save(temp_path)

            return temp_path

        except Exception as e:
            return None

    def haar_crop_detection(self, image_path):
        """使用Haar Cascades检测人脸并计算脖子位置"""
        try:
            # 读取图像
            pil_image = Image.open(image_path).convert('RGBA')
            img_array = np.array(pil_image)
            
            # 转换为RGB用于检测
            rgb_array = img_array[:, :, :3]
            gray = cv2.cvtColor(rgb_array, cv2.COLOR_RGB2GRAY)
            
            # 正面人脸检测
            face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
            faces = face_cascade.detectMultiScale(gray, 1.1, 4)
            
            # 侧面人脸检测
            if len(faces) == 0:
                profile_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_profileface.xml')
                faces = profile_cascade.detectMultiScale(gray, 1.1, 4)
            
            if len(faces) == 0:
                return False, "未检测到人脸"
            
            # 找到最大的人脸
            largest_face = max(faces, key=lambda face: face[2] * face[3])
            x, y, w, h = largest_face
            
            # 检查人脸大小是否合理
            image_height, image_width = gray.shape
            face_area_ratio = (w * h) / (image_width * image_height)
            
            if face_area_ratio < 0.01:
                return False, "检测到的人脸过小"
            
            if face_area_ratio > 0.8:
                return False, "检测到的人脸过大"
            
            # 计算脖子位置
            neck_y = y + h + int(h * 0.3) + 20  # 脸高度30% + 20px安全边距
            
            # 确保不超出图像边界
            neck_y = min(neck_y, image_height - 10)
            
            return True, neck_y
            
        except Exception as e:
            return False, f"Haar检测出错: {e}"

    def sam_crop_analysis(self, image_path):
        """使用SAM-B进行轮廓分析"""
        try:
            # 读取PNG图像
            pil_image = Image.open(image_path).convert('RGBA')
            img_array = np.array(pil_image)
            alpha_array = img_array[:, :, 3]
            
            # 检查是否有内容
            if np.sum(alpha_array > 0) == 0:
                return False, "图像完全透明"
            
            # 创建轮廓mask
            head_mask = (alpha_array > 128).astype(np.uint8) * 255
            contours, _ = cv2.findContours(head_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            if len(contours) == 0:
                return False, "无法找到有效轮廓"
            
            # 找到最大轮廓
            largest_contour = max(contours, key=cv2.contourArea)
            area = cv2.contourArea(largest_contour)
            
            if area < 1000:
                return False, "主体轮廓过小"
            
            # 找到轮廓最低点
            bottom_points = []
            for point in largest_contour:
                x, y = point[0]
                bottom_points.append((x, y))
            
            if not bottom_points:
                return False, "无法找到轮廓点"
            
            # 按Y坐标排序，取最低点
            bottom_points.sort(key=lambda p: p[1], reverse=True)
            neck_y = bottom_points[0][1] + 20  # 加安全边距
            
            # 检查位置是否合理
            height = img_array.shape[0]
            if neck_y > height * 0.95:
                return False, f"脖子位置过低"
            
            if neck_y < height * 0.1:
                return False, f"脖子位置过高"
            
            return True, neck_y
            
        except Exception as e:
            return False, f"SAM分析出错: {e}"

    def crop_and_save(self, image_path, crop_y, base_output_path, filename, is_batch=False):
        """裁剪图像并保存PNG和JPG两个版本"""
        try:
            # 读取图像
            pil_image = Image.open(image_path).convert('RGBA')
            img_array = np.array(pil_image)

            # 裁剪
            cropped_array = img_array[:crop_y, :, :]
            cropped_image = Image.fromarray(cropped_array, 'RGBA')

            if is_batch:
                # 批量处理：创建png和jpg子文件夹
                png_dir = os.path.join(base_output_path, "png")
                jpg_dir = os.path.join(base_output_path, "jpg")
                os.makedirs(png_dir, exist_ok=True)
                os.makedirs(jpg_dir, exist_ok=True)

                png_path = os.path.join(png_dir, f"{filename}.png")
                jpg_path = os.path.join(jpg_dir, f"{filename}.jpg")
            else:
                # 单文件处理：直接保存在指定目录
                png_path = os.path.join(base_output_path, f"{filename}.png")
                jpg_path = os.path.join(base_output_path, f"{filename}.jpg")

            # 保存PNG版本（透明背景）
            cropped_image.save(png_path)

            # 保存JPG版本（白色背景）
            jpg_image = Image.new('RGB', cropped_image.size, (255, 255, 255))
            jpg_image.paste(cropped_image, mask=cropped_image.split()[-1])
            jpg_image.save(jpg_path, quality=95)

            return True

        except Exception as e:
            return False

    def log_failure(self, image_path, reason):
        """记录失败原因到全局日志"""
        log_file = "processing_log.txt"  # 保存在当前工作目录

        with open(log_file, "a", encoding="utf-8") as f:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            filename = os.path.basename(image_path)
            f.write(f"[{timestamp}] {filename} - {reason}\n")

    def get_output_base_path(self, input_path, suffix="processed", batch_output_dir=None):
        """生成输出基础路径"""
        input_file = Path(input_path)

        if batch_output_dir:
            # 批量处理：返回批量输出目录
            return str(batch_output_dir)
        else:
            # 单文件处理：返回原图片同目录
            return str(input_file.parent)

    def process_single_image_silent(self, image_path, batch_output_dir=None):
        """静默处理单张图片"""
        try:
            # 步骤1：RMBG-1.4抠图
            matted_path = self.rmbg_matting(image_path)
            if not matted_path:
                return False, "抠图失败"

            # 步骤2：尝试Haar Cascades裁剪
            haar_success, haar_result = self.haar_crop_detection(matted_path)

            if haar_success:
                base_output_path = self.get_output_base_path(image_path, "haar_cropped", batch_output_dir)
                filename = f"{Path(image_path).stem}_haar_cropped"
                is_batch = batch_output_dir is not None
                if self.crop_and_save(matted_path, haar_result, base_output_path, filename, is_batch):
                    # 清理临时文件
                    os.remove(matted_path)
                    return True, "Haar成功"

            # 步骤3：尝试SAM-B轮廓分析
            sam_success, sam_result = self.sam_crop_analysis(matted_path)

            if sam_success:
                base_output_path = self.get_output_base_path(image_path, "sam_cropped", batch_output_dir)
                filename = f"{Path(image_path).stem}_sam_cropped"
                is_batch = batch_output_dir is not None
                if self.crop_and_save(matted_path, sam_result, base_output_path, filename, is_batch):
                    # 清理临时文件
                    os.remove(matted_path)
                    return True, "SAM成功"

            # 步骤4：所有方法都失败，保存抠图结果
            base_output_path = self.get_output_base_path(image_path, "matted_only", batch_output_dir)
            filename = f"{Path(image_path).stem}_matted_only"
            is_batch = batch_output_dir is not None

            if is_batch:
                # 批量处理：创建png和jpg子文件夹
                png_dir = os.path.join(base_output_path, "png")
                jpg_dir = os.path.join(base_output_path, "jpg")
                os.makedirs(png_dir, exist_ok=True)
                os.makedirs(jpg_dir, exist_ok=True)

                png_path = os.path.join(png_dir, f"{filename}.png")
                jpg_path = os.path.join(jpg_dir, f"{filename}.jpg")
            else:
                # 单文件处理：直接保存在同目录
                png_path = os.path.join(base_output_path, f"{filename}.png")
                jpg_path = os.path.join(base_output_path, f"{filename}.jpg")

            # 保存PNG版本
            matted_image = Image.open(matted_path)
            matted_image.save(png_path)

            # 保存JPG版本（白色背景）
            jpg_image = Image.new('RGB', matted_image.size, (255, 255, 255))
            jpg_image.paste(matted_image, mask=matted_image.split()[-1])
            jpg_image.save(jpg_path, quality=95)

            # 清理临时文件
            os.remove(matted_path)

            # 记录失败原因
            failure_reason = f"Haar: {haar_result}, SAM: {sam_result}"
            self.log_failure(image_path, failure_reason)

            return False, failure_reason

        except Exception as e:
            return False, f"处理异常: {e}"

    def process_batch_silent(self, input_path):
        """静默批量处理"""
        try:
            # 检查目录是否存在
            if not os.path.exists(input_path):
                return False, "目录不存在"

            # 创建批量输出目录
            input_dir = Path(input_path)
            batch_output_dir = f"{input_path}_processed"
            os.makedirs(batch_output_dir, exist_ok=True)

            # 获取所有图片文件
            supported_formats = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff', '*.webp',
                                '*.JPG', '*.JPEG', '*.PNG', '*.BMP', '*.TIFF', '*.WEBP']
            image_files = []
            for pattern in supported_formats:
                image_files.extend(glob.glob(os.path.join(input_path, pattern)))

            if not image_files:
                return False, "未找到图片文件"

            # 逐个处理图片
            success_count = 0
            failed_count = 0
            for image_file in image_files:
                success, method = self.process_single_image_silent(image_file, batch_output_dir)
                if success:
                    success_count += 1
                    self.total_processed += 1
                    # 更新方法统计
                    if "haar" in method.lower():
                        self.haar_success += 1
                    elif "sam" in method.lower():
                        self.sam_success += 1
                else:
                    failed_count += 1
                    self.total_failed += 1

            return True, f"批量处理完成: {success_count}/{len(image_files)} → {batch_output_dir}"

        except Exception as e:
            return False, f"批量处理异常: {e}"

def handle_command(command, processor):
    """处理快捷命令"""
    parts = command.lower().strip().split()
    cmd = parts[0] if parts else ""

    # 基础命令
    if cmd in ['status', 'st']:
        processor.show_queue_status()
        return True
    elif cmd in ['help', 'h', '?']:
        show_help()
        return True
    elif cmd in ['quit', 'exit', 'q']:
        processor.running = False
        print("👋 再见!")
        sys.exit(0)
    elif cmd == 'clear' and len(parts) == 1:
        return clear_queue(processor)
    elif cmd == 'pause':
        return pause_queue(processor)
    elif cmd == 'resume':
        return resume_queue(processor)
    elif cmd == 'open':
        return open_results()
    else:
        return False

def show_help():
    """显示帮助信息"""
    print("\n📖 快捷命令:")
    print("=" * 60)
    print("📈 status/st         - 显示队列状态")
    print("❓ help/h/?          - 显示此帮助")
    print("🚪 quit/exit/q       - 退出程序")
    print()
    print("🎮 队列控制:")
    print("⏸️ pause             - 暂停队列处理")
    print("▶️ resume            - 恢复队列处理")
    print("🗑️ clear             - 清空队列")
    print("📁 open              - 打开当前目录")
    print()
    print("🎯 处理流程:")
    print("1. RMBG-1.4抠图（去除背景，15像素边缘保留）")
    print("2. Haar Cascades快速检测人脸位置")
    print("3. 如果检测失败，使用SAM-B轮廓分析")
    print("4. 生成两种格式：PNG透明背景 + JPG白色背景")
    print("5. 保存到主文件夹下的png/和jpg/子文件夹")
    print()
    print("📁 输出结构:")
    print("unified_results/")
    print("├── success/          # 成功裁剪的图片")
    print("└── failed/           # 失败的图片（仅抠图）")
    print("=" * 60)
    print("💡 直接拖拽图片/文件夹进行处理，可无限添加到队列")

def clear_queue(processor):
    """清空队列"""
    if processor.clear_queue():
        print("✅ 队列已清空")
        processor.show_queue_status()
    else:
        print("❌ 清空队列失败")
    return True

def pause_queue(processor):
    """暂停队列"""
    if processor.pause():
        print("⏸️ 队列已暂停")
        processor.show_queue_status()
    else:
        print("❌ 暂停失败")
    return True

def resume_queue(processor):
    """恢复队列"""
    if processor.resume():
        print("▶️ 队列已恢复")
        processor.show_queue_status()
    else:
        print("❌ 恢复失败")
    return True

def open_results():
    """打开当前工作目录"""
    try:
        os.system("open .")
        print("📁 已打开当前目录")
    except:
        print("❌ 打开文件夹失败")
    return True

def main():
    print("🎯 无限队列统一处理工具")
    print("流程：RMBG-1.4抠图 → Haar裁剪 → SAM备用裁剪")
    print("=" * 60)
    print("💡 使用说明:")
    print("  • 可以无限制拖拽图片/文件夹到队列")
    print("  • 每个结果生成PNG(透明)和JPG(白底)两个版本")
    print("  • 单文件：直接保存在原图片同目录")
    print("  • 批量处理：创建'_processed'文件夹/png/jpg/")
    print("  • 只有失败时才会通知，成功时静默处理")
    print("  • 输入 'status' 查看队列状态")
    print("  • 输入 'help' 查看更多命令")
    print("  • 按 Ctrl+C 或输入 'quit' 退出")
    print("=" * 60)
    print()

    # 创建处理器并启动工作线程
    processor = UnifiedProcessor()
    processor.start_worker()

    try:
        while True:
            print("📎 拖拽文件/文件夹，或输入命令:")
            user_input = input(">>> ").strip()

            if not user_input:
                processor.show_queue_status()
                continue

            # 检查是否是快捷命令
            if handle_command(user_input, processor):
                continue

            # 处理为文件路径
            input_path = clean_path(user_input)

            # 判断是文件还是目录
            if os.path.isfile(input_path):
                processor.add_task(input_path, 'single')
            elif os.path.isdir(input_path):
                processor.add_task(input_path, 'batch')
            else:
                print("❌ 路径不存在或不是有效的文件/文件夹")
                continue

    except KeyboardInterrupt:
        processor.running = False
        print("\n👋 再见!")
        sys.exit(0)
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        processor.running = False

if __name__ == "__main__":
    main()
