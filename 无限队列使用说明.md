# 🎯 无限队列统一处理工具

## 🚀 快速开始

### 启动方式
```bash
# 方法1：双击启动（推荐）
双击 "无限队列统一处理.command" 文件

# 方法2：命令行启动
python3 无限队列统一处理.py
```

## 🔄 处理流程

```
输入图片 → RMBG-1.4抠图 → Haar Cascades裁剪 → SAM-B备用裁剪 → 结果分类
```

### 详细步骤
1. **RMBG-1.4抠图**：所有图片先去除背景
2. **Haar快速裁剪**：检测人脸位置，快速裁剪
3. **SAM备用裁剪**：Haar失败时，用SAM-B轮廓分析
4. **结果分类**：成功保存到success/，失败保存到failed/

## 💡 使用方法

### 基本操作
```
启动程序 → 拖拽文件/文件夹到终端 → 自动处理 → 查看结果
```

### 支持的输入
- **单张图片**：直接拖拽图片文件
- **整个文件夹**：拖拽包含图片的文件夹
- **无限添加**：可以不断添加新任务到队列

### 交互特点
- **静默处理**：成功时不打扰，只有失败才通知
- **实时状态**：随时查看处理进度
- **后台处理**：可以继续添加任务

## 🎮 快捷命令

| 命令 | 功能 |
|------|------|
| `status` 或 `st` | 显示队列状态 |
| `pause` | 暂停队列处理 |
| `resume` | 恢复队列处理 |
| `clear` | 清空队列 |
| `open` | 打开结果文件夹 |
| `help` 或 `?` | 显示帮助信息 |
| `quit` 或 `q` | 退出程序 |

## 📁 输出结构

```
unified_results/
├── success/                    # 成功处理的图片
│   ├── image1_haar_cropped.png # Haar Cascades成功
│   ├── image2_sam_cropped.png  # SAM-B分析成功
│   └── ...
└── failed/                     # 失败的图片
    ├── image3_matted_only.png  # 仅抠图，未裁剪
    ├── image4_matted_only.png
    └── processing_log.txt      # 失败原因记录
```

## 📊 状态显示

### 队列状态示例
```
🔄 正在处理: image.jpg | 队列: 3 | 完成: 15 | Haar: 12 | SAM: 3 | 失败: 2
✅ 队列空闲 | 完成: 18 | Haar: 14 | SAM: 4 | 失败: 2
⏸️ 队列已暂停 | 队列: 5 | 完成: 10 | Haar: 8 | SAM: 2 | 失败: 1
```

### 状态说明
- **队列**：等待处理的任务数
- **完成**：成功处理的总数
- **Haar**：Haar Cascades成功数
- **SAM**：SAM-B分析成功数
- **失败**：无法裁剪的数量

## 🎨 支持格式

### 输入格式
- JPG, JPEG
- PNG
- BMP
- TIFF
- WebP

### 输出格式
- PNG（保持透明背景）

## ⚡ 性能表现

| 处理阶段 | 耗时 | 成功率 |
|----------|------|--------|
| RMBG-1.4抠图 | 8-10秒 | ~95% |
| Haar检测裁剪 | 0.1秒 | ~70% |
| SAM轮廓裁剪 | 1-3秒 | ~90% |
| **总体成功率** | - | **~95%** |

## 💡 使用技巧

### 路径输入
- 直接拖拽文件/文件夹到终端窗口
- 自动处理路径中的空格和特殊字符
- 支持中文路径和文件名

### 批量处理
- 可以同时拖拽多个文件夹
- 处理过程中可以继续添加新任务
- 暂停/恢复功能方便控制处理节奏

### 结果管理
- 成功和失败的图片分开存放
- 失败的图片保留抠图效果，可手动处理
- 查看processing_log.txt了解失败原因

## 🔧 故障排除

### 常见问题
**Q: 程序卡住不动？**
A: 输入 `status` 查看状态，或 `pause` 暂停后 `resume` 恢复

**Q: 处理失败率高？**
A: 检查图片质量，侧身、遮挡、光线问题会影响检测

**Q: 想重新处理失败的图片？**
A: 从failed文件夹拖拽图片重新处理

**Q: 如何停止处理？**
A: 输入 `pause` 暂停，或 `clear` 清空队列

## 🎉 优势特点

1. **无限队列**：可以无限制添加任务
2. **静默处理**：成功时不打扰用户
3. **智能分级**：多重检测方案确保成功率
4. **实时控制**：随时暂停、恢复、清空队列
5. **结果分类**：成功失败分开存放
6. **用户友好**：拖拽操作，简单直观

---

🎯 **享受无限制的智能图像处理体验！**
